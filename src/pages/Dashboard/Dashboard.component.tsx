import React from 'react'
import { motion } from 'framer-motion'
import MainLayout from '../../components/layout/MainLayout'
import WelcomeSection from './sub-components/WelcomeSection'
import LearningStatsCard from './sub-components/LearningStatsCard'
import PerformanceCard from './sub-components/PerformanceCard'
import LeaderboardCard from './sub-components/LeaderboardCard'
import RecentActivityCard from './sub-components/RecentActivityCard'
import { DashboardComponentProps } from './types'

/**
 * Dashboard Component - Enhanced modern UI with improved information hierarchy
 */
const DashboardComponent: React.FC<DashboardComponentProps> = ({
  user,
  stats,
  statsLoading,
  statsError,
  onRefreshStats,
  leaderboard,
  leaderboardLoading,
  leaderboardError,
  onRefreshLeaderboard
}) => {
  // Minimal animation variants for sleek feel
  const cardVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.2
      }
    }
  }

  return (
    <MainLayout
      title="Dashboard"
      description={`Welcome back, ${user.full_name || user.username}!`}
    >
      {/* Floating Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        {Array.from({ length: 8 }, (_, i) => (
          <motion.div
            key={i}
            className="absolute w-32 h-32 bg-gradient-to-br from-blue-100/20 to-purple-100/20 dark:from-blue-900/10 dark:to-purple-900/10 rounded-full blur-xl"
            animate={{
              x: [0, Math.random() * 100 - 50],
              y: [0, Math.random() * 100 - 50],
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{
              duration: Math.random() * 10 + 10,
              repeat: Infinity,
              delay: Math.random() * 5
            }}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`
            }}
          />
        ))}
      </div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="relative z-10 h-screen w-full flex flex-col p-3 lg:p-4 overflow-hidden max-h-screen"
      >
        {/* Main Content Area - Two Column Layout with Hero */}
        <motion.div
          className="flex-1 flex flex-col lg:flex-row gap-6 min-h-0 overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.6 }}
        >
          {/* Left Column - Hero + Learning Stats + Performance + Recent Activity */}
          <motion.div
            className="lg:w-[70%] flex flex-col gap-4 overflow-hidden"
            whileHover={{ scale: 1.001 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          >
            {/* Welcome Section with Daily Inspiration */}
            <motion.div
              className="flex-shrink-0"
              whileHover={{ scale: 1.002 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            >
              <WelcomeSection user={user} cardVariants={cardVariants} />
            </motion.div>

            {/* Learning Stats Card */}
            <div className="flex-shrink-0">
              <LearningStatsCard
                stats={stats}
                loading={statsLoading}
                error={statsError}
                onRefresh={onRefreshStats}
                cardVariants={cardVariants}
              />
            </div>

            {/* Performance Card */}
            <div className="flex-shrink-0">
              <PerformanceCard
                stats={stats}
                loading={statsLoading}
                error={statsError}
                onRefresh={onRefreshStats}
                cardVariants={cardVariants}
              />
            </div>

            {/* Recent Activity Card */}
            <div className="flex-1 min-h-0">
              <RecentActivityCard cardVariants={cardVariants} />
            </div>
          </motion.div>

          {/* Right Column - Leaderboard */}
          <motion.div
            className="lg:w-[30%] flex-1 overflow-hidden"
            whileHover={{ scale: 1.001 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          >
            <div className="h-full">
              <LeaderboardCard
                leaderboard={leaderboard}
                loading={leaderboardLoading}
                error={leaderboardError}
                onRefresh={onRefreshLeaderboard}
                cardVariants={cardVariants}
              />
            </div>
          </motion.div>
        </motion.div>
      </motion.div>
    </MainLayout>
  )
}

export default DashboardComponent
