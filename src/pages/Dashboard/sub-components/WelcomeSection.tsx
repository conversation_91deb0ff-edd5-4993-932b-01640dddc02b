import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { User } from '../../../types/auth'
import { Calendar, Clock, Award, Sparkles, Quote, Heart, Star, Lightbulb } from 'lucide-react'
import { cn } from '../../../utils/cn'

interface WelcomeSectionProps {
  user: User
  cardVariants: any
}

interface Quote {
  text: string
  author: string
  category: 'learning' | 'motivation' | 'success' | 'wisdom'
}

const quotes: Quote[] = [
  {
    text: "The beautiful thing about learning is that no one can take it away from you.",
    author: "B.B. King",
    category: "learning"
  },
  {
    text: "Education is the most powerful weapon which you can use to change the world.",
    author: "<PERSON>",
    category: "learning"
  },
  {
    text: "Learning never exhausts the mind.",
    author: "<PERSON> da <PERSON>",
    category: "learning"
  },
  {
    text: "Success is not final, failure is not fatal: it is the courage to continue that counts.",
    author: "<PERSON>",
    category: "motivation"
  },
  {
    text: "The expert in anything was once a beginner.",
    author: "<PERSON>",
    category: "wisdom"
  }
]

const categoryIcons = {
  learning: Lightbulb,
  motivation: Heart,
  success: Star,
  wisdom: Quote
}

const WelcomeSection: React.FC<WelcomeSectionProps> = ({
  user,
  cardVariants
}) => {
  const [currentQuoteIndex, setCurrentQuoteIndex] = useState(0)

  // Auto-rotate quotes every 8 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentQuoteIndex((prev) => (prev + 1) % quotes.length)
    }, 8000)

    return () => clearInterval(interval)
  }, [])

  const currentQuote = quotes[currentQuoteIndex]
  const IconComponent = categoryIcons[currentQuote.category]
  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return 'Good morning'
    if (hour < 17) return 'Good afternoon'
    return 'Good evening'
  }

  // Get current date in a readable format
  const getCurrentDate = () => {
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }
    return new Date().toLocaleDateString(undefined, options)
  }

  return (
    <motion.div
      variants={cardVariants}
      className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-indigo-950/30 border border-blue-100 dark:border-indigo-900/50 rounded-xl p-3 shadow-lg"
    >
      {/* Top Row - Welcome Message and Profile */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-3 mb-2">
        <div className="space-y-1">
          <div className="flex items-center gap-1.5">
            <Sparkles className="h-4 w-4 text-amber-500" />
            <span className="text-xs font-medium text-blue-600 dark:text-blue-400">
              {getCurrentDate()}
            </span>
          </div>

          <h1 className="text-lg sm:text-xl font-bold text-slate-900 dark:text-white flex items-center gap-1.5">
            {getGreeting()}, {user.full_name || user.username}!
            <span className="text-lg sm:text-xl">👋</span>
          </h1>

          <p className="text-slate-600 dark:text-slate-300 text-sm">
            Ready to continue your Nepali learning journey today?
          </p>
        </div>

        <div className="flex items-center gap-3">
          {user.profile_picture ? (
            <div className="relative">
              <img
                src={user.profile_picture}
                alt={user.username}
                className="w-10 h-10 rounded-full border-2 border-white dark:border-slate-700 shadow-sm"
              />
              <div className="absolute -bottom-1 -right-1 bg-green-500 w-3 h-3 rounded-full border-2 border-white dark:border-slate-800"></div>
            </div>
          ) : (
            <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center text-white font-bold text-lg border-2 border-white dark:border-slate-700 shadow-sm">
              {(user.full_name || user.username).charAt(0).toUpperCase()}
            </div>
          )}

          <div className="hidden sm:block h-8 w-px bg-slate-200 dark:bg-slate-700"></div>

          <button className="hidden sm:flex items-center gap-1.5 px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors shadow-sm text-sm">
            <Award className="h-3.5 w-3.5" />
            <span className="font-medium">Start Learning</span>
          </button>
        </div>
      </div>

      {/* Bottom Row - Daily Inspiration Quote */}
      <div className="border-t border-blue-200 dark:border-indigo-800/50 pt-2">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentQuoteIndex}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
            className="flex items-center gap-3"
          >
            {/* Quote Icon */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center"
            >
              <IconComponent className="h-4 w-4 text-white" />
            </motion.div>

            {/* Quote Content */}
            <div className="flex-1 min-w-0">
              <motion.blockquote
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.4 }}
                className="text-sm font-medium text-slate-700 dark:text-slate-300 italic truncate"
              >
                "{currentQuote.text}"
              </motion.blockquote>
              <motion.cite
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4, duration: 0.4 }}
                className="text-xs text-slate-500 dark:text-slate-400 not-italic"
              >
                — {currentQuote.author}
              </motion.cite>
            </div>

            {/* Quote indicator dots */}
            <div className="hidden sm:flex gap-1">
              {quotes.map((_, index) => (
                <div
                  key={index}
                  className={cn(
                    "w-1.5 h-1.5 rounded-full transition-all duration-300",
                    index === currentQuoteIndex
                      ? "bg-purple-500"
                      : "bg-slate-300 dark:bg-slate-600"
                  )}
                />
              ))}
            </div>
          </motion.div>
        </AnimatePresence>
      </div>
    </motion.div>
  )
}

export default WelcomeSection
