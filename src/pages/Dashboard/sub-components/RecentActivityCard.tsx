import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import {
  Activity,
  Clock,
  CheckCircle,
  Headphones,
  FileText,
  ChevronRight,
  Star
} from 'lucide-react'
import { cn } from '../../../utils/cn'

interface RecentActivityCardProps {
  cardVariants: any
  recentActivities?: Array<{
    id: string
    title: string
    type: 'audio' | 'text'
    completedAt: string
    score?: number
    maxScore?: number
    path: string
  }>
  loading?: boolean
}

const RecentActivityCard: React.FC<RecentActivityCardProps> = ({ 
  cardVariants,
  recentActivities = [],
  loading = false
}) => {
  // Format date as time ago
  const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return `${Math.floor(diffInSeconds / 2592000)}mo ago`
  }

  // Sample data if none provided
  const sampleActivities = [
    {
      id: '1',
      title: 'Basic Nepali Greetings',
      type: 'audio' as const,
      completedAt: new Date(Date.now() - 3600000).toISOString(),
      score: 85,
      maxScore: 100,
      path: '/tasks/sample-1'
    },
    {
      id: '2',
      title: 'Common Phrases Practice',
      type: 'text' as const,
      completedAt: new Date(Date.now() - 86400000).toISOString(),
      score: 70,
      maxScore: 100,
      path: '/tasks/sample-2'
    },
    {
      id: '3',
      title: 'Numbers and Counting',
      type: 'audio' as const,
      completedAt: new Date(Date.now() - 172800000).toISOString(),
      score: 90,
      maxScore: 100,
      path: '/tasks/sample-3'
    }
  ]

  const activities = recentActivities.length > 0 ? recentActivities : sampleActivities

  return (
    <motion.div
      variants={cardVariants}
      className="bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-xl overflow-hidden"
    >
      <div className="p-3 border-b border-slate-200 dark:border-slate-700 flex items-center justify-between">
        <h3 className="text-base font-semibold text-slate-900 dark:text-white flex items-center gap-2">
          <Activity className="h-4 w-4 text-blue-500" />
          Recent Activity
        </h3>
        <Link to="/tasks" className="text-xs text-blue-600 dark:text-blue-400 hover:underline flex items-center">
          View all
          <ChevronRight className="h-3 w-3 ml-1" />
        </Link>
      </div>

      <div className="divide-y divide-slate-200 dark:divide-slate-700">
        {activities.map((activity) => (
          <Link to={activity.path} key={activity.id}>
            <div className="p-3 hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors">
              <div className="flex items-center gap-2">
                <div className={cn(
                  "p-1.5 rounded-lg flex-shrink-0",
                  activity.type === 'audio'
                    ? "bg-blue-100 dark:bg-blue-900/20"
                    : "bg-purple-100 dark:bg-purple-900/20"
                )}>
                  {activity.type === 'audio' ? (
                    <Headphones className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                  ) : (
                    <FileText className="h-3 w-3 text-purple-600 dark:text-purple-400" />
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-slate-900 dark:text-white text-xs truncate">
                    {activity.title}
                  </h4>

                  <div className="flex items-center gap-2 mt-0.5">
                    <div className="flex items-center text-xs text-slate-500 dark:text-slate-400">
                      <Clock className="h-2.5 w-2.5 mr-1" />
                      {formatTimeAgo(activity.completedAt)}
                    </div>

                    {activity.score !== undefined && activity.maxScore !== undefined && (
                      <div className="flex items-center text-xs text-slate-500 dark:text-slate-400">
                        <Star className="h-2.5 w-2.5 mr-1 text-amber-500" />
                        {activity.score}/{activity.maxScore}
                      </div>
                    )}

                    <div className="flex items-center text-xs text-green-600 dark:text-green-400">
                      <CheckCircle className="h-2.5 w-2.5 mr-1" />
                      Completed
                    </div>
                  </div>
                </div>
                
                <ChevronRight className="h-3 w-3 text-slate-400" />
              </div>
            </div>
          </Link>
        ))}
      </div>

      {activities.length === 0 && !loading && (
        <div className="p-8 text-center">
          <p className="text-slate-500 dark:text-slate-400">No recent activities found</p>
        </div>
      )}

      {loading && (
        <div className="p-8 text-center">
          <p className="text-slate-500 dark:text-slate-400">Loading activities...</p>
        </div>
      )}
    </motion.div>
  )
}

export default RecentActivityCard