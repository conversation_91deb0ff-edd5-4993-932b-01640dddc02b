import React from 'react'
import { motion } from 'framer-motion'
import {
  Trophy,
  Crown,
  Medal,
  Star,
  RefreshCw,
  User,
  Loader2,
  TrendingUp,
  Target,
  Zap
} from 'lucide-react'
import { cn } from '../../../utils/cn'
import { LeaderboardResponse } from '../../../services/stats/statsService'

interface LeaderboardCardProps {
  leaderboard: LeaderboardResponse | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

const LeaderboardCard: React.FC<LeaderboardCardProps> = ({
  leaderboard,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="h-6 w-6 text-yellow-400" />
      case 2:
        return <Medal className="h-6 w-6 text-slate-400" />
      case 3:
        return <Medal className="h-6 w-6 text-amber-500" />
      default:
        return <div className="w-6 h-6 rounded-full bg-slate-200 dark:bg-slate-700 flex items-center justify-center text-xs font-bold text-slate-600 dark:text-slate-400">{rank}</div>
    }
  }

  const getRankBadgeColor = (rank: number) => {
    switch (rank) {
      case 1:
        return "bg-gradient-to-r from-yellow-400 to-yellow-600 text-white shadow-lg shadow-yellow-500/25"
      case 2:
        return "bg-gradient-to-r from-slate-300 to-slate-500 text-white shadow-lg shadow-slate-500/25"
      case 3:
        return "bg-gradient-to-r from-amber-400 to-amber-600 text-white shadow-lg shadow-amber-500/25"
      default:
        return "bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-400"
    }
  }

  const getRankGlow = (rank: number) => {
    switch (rank) {
      case 1:
        return "ring-2 ring-yellow-200 dark:ring-yellow-800 bg-gradient-to-r from-yellow-50 to-amber-50 dark:from-yellow-900/20 dark:to-amber-900/20"
      case 2:
        return "ring-2 ring-slate-200 dark:ring-slate-700 bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800/50 dark:to-slate-700/50"
      case 3:
        return "ring-2 ring-amber-200 dark:ring-amber-800 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20"
      default:
        return "bg-white dark:bg-slate-900"
    }
  }

  return (
    <motion.div
      variants={cardVariants}
      whileHover={{
        scale: 1.02,
        boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
      }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      className="bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-xl overflow-hidden h-full max-h-full flex flex-col shadow-lg"
    >
      {/* Compact Header */}
      <div className="p-3 border-b border-slate-200 dark:border-slate-700 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="p-1.5 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-lg shadow-sm">
            <Trophy className="h-4 w-4 text-white" />
          </div>
          <h3 className="text-base font-semibold text-slate-900 dark:text-white">
            Leaderboard
          </h3>
        </div>
        <button
          onClick={onRefresh}
          disabled={loading}
          className={cn(
            "p-1.5 rounded-lg transition-colors",
            "bg-slate-100 hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700",
            "focus:outline-none focus:ring-2 focus:ring-amber-500",
            loading && "opacity-50 cursor-not-allowed"
          )}
        >
          <RefreshCw className={cn("h-3.5 w-3.5 text-slate-600 dark:text-slate-400", loading && "animate-spin")} />
        </button>
      </div>

      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6 p-4 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-xl"
        >
          <p className="text-red-600 dark:text-red-400 text-sm font-medium">{error}</p>
        </motion.div>
      )}

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-12 w-12 animate-spin text-purple-500 mx-auto mb-4" />
            <p className="text-slate-600 dark:text-slate-400 font-medium">Loading rankings...</p>
          </div>
        </div>
      ) : (
        <div className="flex-1 overflow-auto">
          <div className="space-y-1 p-2">
          {leaderboard?.data?.length === 0 ? (
            <div className="text-center py-8">
              <div className="p-3 bg-slate-100 dark:bg-slate-800 rounded-full w-fit mx-auto mb-3">
                <Trophy className="h-8 w-8 text-slate-400" />
              </div>
              <p className="text-slate-600 dark:text-slate-400 font-medium text-sm">No rankings available yet</p>
              <p className="text-slate-500 dark:text-slate-500 text-xs mt-1">Complete some tasks to see the leaderboard!</p>
            </div>
          ) : (
            leaderboard?.data?.map((entry, index) => (
              <div
                key={entry.user_id}
                className={cn(
                  "relative overflow-hidden border p-2 transition-all duration-200",
                  "hover:bg-slate-50 dark:hover:bg-slate-800/50",
                  index !== leaderboard.data.length - 1 && "border-b",
                  getRankGlow(entry.rank)
                )}
              >
                <div className="flex items-center gap-2">
                  {/* Rank Badge */}
                  <div className="flex-shrink-0">
                    <div className={cn(
                      "flex items-center justify-center w-6 h-6 rounded-lg font-bold text-xs shadow-sm",
                      getRankBadgeColor(entry.rank)
                    )}>
                      {entry.rank <= 3 ? getRankIcon(entry.rank) : entry.rank}
                    </div>
                  </div>

                  {/* Profile Picture */}
                  <div className="flex-shrink-0">
                    {entry.profile_picture ? (
                      <img
                        src={entry.profile_picture}
                        alt={entry.username}
                        className="w-6 h-6 rounded-lg object-cover border border-slate-200 dark:border-slate-700 shadow-sm"
                      />
                    ) : (
                      <div className="w-6 h-6 rounded-lg bg-slate-200 dark:bg-slate-700 flex items-center justify-center border border-slate-300 dark:border-slate-600 shadow-sm">
                        <User className="h-3 w-3 text-slate-500 dark:text-slate-400" />
                      </div>
                    )}
                  </div>

                  {/* User Info */}
                  <div className="flex-1 min-w-0">
                    <h5 className="font-semibold text-slate-900 dark:text-white truncate text-sm">
                      {entry.username}
                    </h5>
                    <div className="flex items-center gap-2 mt-0.5">
                      <div className="flex items-center gap-0.5 text-[10px] text-slate-600 dark:text-slate-400 bg-slate-100 dark:bg-slate-800 px-1 py-0.5 rounded-full">
                        <Target className="h-2 w-2" />
                        <span>{entry.total_attempts}</span>
                      </div>
                      <div className="flex items-center gap-0.5 text-[10px] text-slate-600 dark:text-slate-400 bg-slate-100 dark:bg-slate-800 px-1 py-0.5 rounded-full">
                        <Zap className="h-2 w-2" />
                        <span>{entry.accuracy}%</span>
                      </div>
                    </div>
                  </div>

                  {/* Score */}
                  <div className="text-right">
                    <div className="text-base font-bold text-slate-900 dark:text-white">
                      {entry.total_score.toLocaleString()}
                    </div>
                    <div className="text-[10px] text-slate-600 dark:text-slate-400">
                      points
                    </div>
                  </div>
                </div>

                {/* Top 3 Achievement Badge - Positioned safely */}
                {entry.rank <= 3 && (
                  <div className="absolute top-1 left-1">
                    <div className={cn(
                      "px-1.5 py-0.5 rounded-full text-[10px] font-bold shadow-sm",
                      entry.rank === 1 && "bg-yellow-400 text-yellow-900",
                      entry.rank === 2 && "bg-slate-400 text-slate-900",
                      entry.rank === 3 && "bg-amber-400 text-amber-900"
                    )}>
                      #{entry.rank}
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
          </div>
        </div>
      )}

      {/* Compact Leaderboard Footer */}
      {leaderboard?.meta && !loading && leaderboard.data.length > 0 && (
        <div className="p-2 bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800/30 dark:to-slate-800/10 border-t border-slate-200 dark:border-slate-700">
          <div className="flex items-center justify-between text-[10px]">
            <div className="flex items-center gap-1 text-slate-600 dark:text-slate-400">
              <TrendingUp className="h-3 w-3 text-green-500" />
              <span>
                {leaderboard.data.length} of {leaderboard.meta.total} learners
              </span>
            </div>
            <div className="bg-amber-100 dark:bg-amber-900/20 text-amber-800 dark:text-amber-300 px-1.5 py-0.5 rounded-full font-medium">
              Updated 
            </div>
          </div>
        </div>
      )}
    </motion.div>
  )
}

export default LeaderboardCard
