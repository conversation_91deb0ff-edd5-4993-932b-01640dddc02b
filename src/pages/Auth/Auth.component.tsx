import React from 'react'
import { motion } from 'framer-motion'
import { useTheme } from '../../services/theme/ThemeProvider'
import { AuthComponentProps, LoginFormData, SignupFormData } from './types'
import FormWrapper from './shared/FormWrapper'
import ModeSwitch from './shared/ModeSwitch'
import GoogleAuthComponent from './OAuth/Google/GoogleAuth.component'
import LoginComponent from './Login/Login.component'
import SignupComponent from './Signup/Signup.component'

/**
 * Auth Component - Main authentication component that renders login or signup
 */
const AuthComponent: React.FC<AuthComponentProps> = ({
  mode,
  formData,
  errors,
  isLoading,
  onModeChange,
  onInputChange,
  onSubmit,
  onGoogleAuth,
  onClearError,
}) => {
  const { theme } = useTheme()

  return (
    <motion.div
      initial={{ opacity: 0, x: '100%' }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: '100%' }}
      transition={{
        type: "spring",
        stiffness: 100,
        damping: 20,
        duration: 0.6
      }}
      className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 via-blue-50 to-pink-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 px-3 sm:px-4 md:px-6 lg:px-8 relative overflow-hidden"
    >
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Floating Particles */}
        {Array.from({ length: 20 }, (_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full"
            style={{
              width: `${4 + Math.random() * 8}px`,
              height: `${4 + Math.random() * 8}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              background: i % 3 === 0 ?
                'linear-gradient(45deg, #3b82f6, #8b5cf6)' :
                i % 3 === 1 ?
                'linear-gradient(45deg, #8b5cf6, #ec4899)' :
                'linear-gradient(45deg, #06b6d4, #3b82f6)'
            }}
            animate={{
              y: [0, -100, 0],
              x: [0, Math.random() * 50 - 25, 0],
              opacity: [0.3, 0.8, 0.3],
              scale: [0.5, 1.2, 0.5]
            }}
            transition={{
              duration: Math.random() * 6 + 4,
              repeat: Infinity,
              delay: Math.random() * 3,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Geometric Shapes */}
        {Array.from({ length: 5 }, (_, i) => (
          <motion.div
            key={`shape-${i}`}
            className="absolute"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              rotate: [0, 360],
              scale: [1, 1.2, 1],
              opacity: [0.1, 0.3, 0.1]
            }}
            transition={{
              duration: 8 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 2,
              ease: "linear"
            }}
          >
            {i % 2 === 0 ? (
              <div className="w-16 h-16 border-2 border-blue-300/30 rounded-lg" />
            ) : (
              <div className="w-12 h-12 border-2 border-purple-300/30 rounded-full" />
            )}
          </motion.div>
        ))}
      </div>
      <motion.div
        initial={{ opacity: 0, scale: 0.9, x: 50 }}
        animate={{ opacity: 1, scale: 1, x: 0 }}
        transition={{
          delay: 0.2,
          type: "spring",
          stiffness: 150,
          damping: 25
        }}
      >
        <FormWrapper>
        {/* Render Login or Signup Component */}
        {mode === 'login' ? (
          <LoginComponent
            formData={formData as LoginFormData}
            errors={errors}
            isLoading={isLoading}
            onInputChange={onInputChange}
            onSubmit={onSubmit}
            onClearError={onClearError}
          />
        ) : (
          <SignupComponent
            formData={formData as SignupFormData}
            errors={errors}
            isLoading={isLoading}
            onInputChange={onInputChange}
            onSubmit={onSubmit}
            onClearError={onClearError}
          />
        )}

        {/* Divider */}
        <div className="relative my-4">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300 dark:border-gray-600" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
              Or
            </span>
          </div>
        </div>

        {/* Google Sign-in Button */}
        <div className="mb-4">
          <GoogleAuthComponent
            onSuccess={onGoogleAuth}
            onError={() => console.error('Google sign-in failed')}
            disabled={isLoading}
          />
        </div>

        {/* Mode Switch */}
        <div className="mt-4">
          <ModeSwitch
            mode={mode}
            onModeChange={onModeChange}
            disabled={isLoading}
          />
        </div>
        </FormWrapper>
      </motion.div>
    </motion.div>
  )
}

export default AuthComponent
