import React, { useEffect, useRef, useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import * as anime from 'animejs'
import {
  Volume2,
  BookOpen,
  Users,
  Award,
  Play,
  ArrowRight,
  Sparkles,
  Headphones,
  Mic,
  X,
  ChevronLeft
} from 'lucide-react'
import SVGAnimatedCharacter from '../../components/ui/SVGAnimatedCharacter'
import AnimatedCharacter from '../../components/ui/AnimatedCharacter'

/**
 * Animated Home Page with sound waves and beautiful animations
 */
const Home: React.FC = () => {
  const soundWaveRef = useRef<HTMLDivElement>(null)
  const heroRef = useRef<HTMLDivElement>(null)
  const loginFormRef = useRef<HTMLDivElement>(null)
  const [showLoginForm, setShowLoginForm] = useState(false)
  const navigate = useNavigate()

  useEffect(() => {
    try {
      // Animate sound waves
      if (soundWaveRef.current && anime.default) {
        const waves = soundWaveRef.current.children
        anime.default({
          targets: waves,
          scaleY: [0.5, 2, 0.5],
          duration: 2000,
          delay: anime.default.stagger(100),
          loop: true,
          easing: 'easeInOutSine',
          direction: 'alternate'
        })
      }

      // Animate hero section
      if (heroRef.current && anime.default) {
        anime.default({
          targets: heroRef.current.children,
          translateY: [50, 0],
          opacity: [0, 1],
          duration: 1000,
          delay: anime.default.stagger(200),
          easing: 'easeOutCubic'
        })
      }

      // Floating animation for login form
      if (loginFormRef.current && anime.default) {
        anime.default({
          targets: loginFormRef.current,
          translateY: [-10, 10],
          duration: 3000,
          loop: true,
          direction: 'alternate',
          easing: 'easeInOutSine'
        })
      }
    } catch (error) {
      console.warn('Anime.js animations failed to load:', error)
    }
  }, [])

  const generateSoundWaves = () => {
    return Array.from({ length: 20 }, (_, i) => (
      <div
        key={i}
        className="bg-gradient-to-t from-blue-400 to-purple-500 rounded-full mx-1"
        style={{
          width: '4px',
          height: `${20 + Math.random() * 60}px`,
          animationDelay: `${i * 0.1}s`
        }}
      />
    ))
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 overflow-hidden">
      {/* Background Particles */}
      <div className="absolute inset-0 overflow-hidden">
        {Array.from({ length: 50 }, (_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-blue-300/30 rounded-full"
            animate={{
              x: [0, Math.random() * 100 - 50],
              y: [0, Math.random() * 100 - 50],
              opacity: [0, 1, 0]
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              delay: Math.random() * 2
            }}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`
            }}
          />
        ))}
      </div>

      {/* Floating Characters */}
      <div className="absolute top-20 left-10">
        <AnimatedCharacter
          size="sm"
          character="wizard"
          mood="thinking"
          className="opacity-60"
        />
      </div>
      <div className="absolute top-40 right-20">
        <AnimatedCharacter
          size="sm"
          character="scientist"
          mood="focused"
          className="opacity-50"
        />
      </div>
      <div className="absolute bottom-40 left-20">
        <AnimatedCharacter
          size="sm"
          character="artist"
          mood="happy"
          className="opacity-70"
        />
      </div>

      <div className="relative z-10 flex min-h-screen">
        {/* Main Content - Responsive width */}
        <div className={`${showLoginForm ? 'lg:w-[60%]' : 'lg:w-[90%]'} flex-1 flex flex-col justify-center items-center px-8 py-12 transition-all duration-500`}>
          {/* Hero Section */}
          <div ref={heroRef} className="text-center max-w-4xl mx-auto">
            {/* Logo and Title */}
            <motion.div
              className="mb-8"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.8, type: "spring" }}
            >
              <div className="flex items-center justify-center gap-4 mb-6">
                <SVGAnimatedCharacter
                  size={100}
                  mood="excited"
                  className="animate-pulse"
                />
                <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-2xl">
                  <Volume2 className="h-12 w-12 text-white" />
                </div>
                <AnimatedCharacter
                  size="lg"
                  character="hero"
                  mood="celebrating"
                />
              </div>
              <h1 className="text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                Audio Quiz Genie
              </h1>
              <p className="text-2xl text-gray-600 dark:text-gray-300 mb-2">
                Learn Nepali Through Interactive Audio Quizzes
              </p>
              <p className="text-lg text-gray-500 dark:text-gray-400">
                Master pronunciation, vocabulary, and comprehension with AI-powered feedback
              </p>
            </motion.div>

            {/* Sound Wave Visualization */}
            <div className="mb-12">
              <div ref={soundWaveRef} className="flex items-end justify-center h-32 mb-4">
                {generateSoundWaves()}
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                🎵 Interactive Audio Learning Experience
              </p>
            </div>

            {/* Features Grid */}
            <div className="grid md:grid-cols-3 gap-8 mb-12">
              <motion.div
                className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 group"
                whileHover={{
                  scale: 1.08,
                  y: -10,
                  boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
                }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
              >
                <motion.div
                  className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300"
                >
                  <Headphones className="h-6 w-6 text-blue-600 group-hover:text-blue-700" />
                </motion.div>
                <h3 className="text-xl font-semibold mb-2 group-hover:text-blue-600 transition-colors">Audio Learning</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Listen, learn, and practice with native pronunciation guides
                </p>
              </motion.div>

              <motion.div
                className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 group"
                whileHover={{
                  scale: 1.08,
                  y: -10,
                  boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
                }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
              >
                <motion.div
                  className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300"
                >
                  <Mic className="h-6 w-6 text-purple-600 group-hover:text-purple-700" />
                </motion.div>
                <h3 className="text-xl font-semibold mb-2 group-hover:text-purple-600 transition-colors">Speech Recognition</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  AI-powered feedback on your pronunciation and speaking skills
                </p>
              </motion.div>

              <motion.div
                className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 group"
                whileHover={{
                  scale: 1.08,
                  y: -10,
                  boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
                }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
              >
                <motion.div
                  className="w-12 h-12 bg-pink-100 dark:bg-pink-900/30 rounded-xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300"
                >
                  <Award className="h-6 w-6 text-pink-600 group-hover:text-pink-700" />
                </motion.div>
                <h3 className="text-xl font-semibold mb-2 group-hover:text-pink-600 transition-colors">Progress Tracking</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Track your learning journey with detailed analytics
                </p>
              </motion.div>
            </div>

            {/* CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-6 justify-center"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.6 }}
            >
              <Link to="/signup">
                <motion.button
                  className="px-10 py-5 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl font-semibold text-lg shadow-xl hover:shadow-2xl transition-all duration-300 flex items-center gap-3 group"
                  whileHover={{
                    scale: 1.08,
                    boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
                    y: -3
                  }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <Play className="h-5 w-5 group-hover:scale-110 transition-transform" />
                  Start Learning Now
                  <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </motion.button>
              </Link>

              <Link to="/dashboard">
                <motion.button
                  className="px-10 py-5 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-gray-800 dark:text-white rounded-2xl font-semibold text-lg shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-200 dark:border-gray-700 group"
                  whileHover={{
                    scale: 1.08,
                    boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
                    y: -3
                  }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <BookOpen className="h-5 w-5 inline mr-3 group-hover:scale-110 transition-transform" />
                  Explore Dashboard
                </motion.button>
              </Link>
            </motion.div>
          </div>
        </div>

        {/* Login Preview/Form - Responsive */}
        <div className={`${showLoginForm ? 'lg:w-[40%]' : 'lg:w-[10%]'} bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm border-l border-white/20 flex flex-col justify-center transition-all duration-500 overflow-hidden`}>
          <AnimatePresence mode="wait">
            {!showLoginForm ? (
              /* Login Preview - Small */
              <motion.div
                key="preview"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 50 }}
                transition={{ duration: 0.3 }}
                className="p-4 cursor-pointer h-full flex flex-col justify-center items-center"
                onClick={() => setShowLoginForm(true)}
              >
                <div className="text-center space-y-4">
                  <motion.div
                    className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <Sparkles className="h-6 w-6 text-white" />
                  </motion.div>
                  <div className="transform -rotate-90 whitespace-nowrap">
                    <p className="text-sm font-semibold text-gray-800 dark:text-white">
                      Sign In
                    </p>
                  </div>
                  <motion.div
                    animate={{ y: [0, -5, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="text-blue-600"
                  >
                    <ArrowRight className="h-4 w-4 transform rotate-90" />
                  </motion.div>
                </div>
              </motion.div>
            ) : (
              /* Full Login Form */
              <motion.div
                key="form"
                initial={{ opacity: 0, x: 100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 100 }}
                transition={{ duration: 0.5, type: "spring", stiffness: 100 }}
                className="p-8 h-full flex flex-col justify-center"
              >
                <div className="space-y-6">
                  {/* Close Button */}
                  <div className="flex justify-between items-center">
                    <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
                      Welcome Back
                    </h2>
                    <button
                      onClick={() => setShowLoginForm(false)}
                      className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full transition-colors"
                    >
                      <X className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                    </button>
                  </div>

                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Sparkles className="h-8 w-8 text-white" />
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Continue your learning journey
                    </p>
                  </div>

                  <button
                    onClick={() => navigate('/login')}
                    className="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                  >
                    Sign In
                  </button>

                  <div className="text-center">
                    <p className="text-gray-600 dark:text-gray-400 text-sm">
                      New to Audio Quiz Genie?
                    </p>
                    <button
                      onClick={() => navigate('/signup')}
                      className="text-blue-600 hover:text-blue-700 font-semibold text-sm"
                    >
                      Create Account
                    </button>
                  </div>

                  {/* Quick Stats */}
                  <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4 space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Active Learners</span>
                      <span className="font-semibold text-blue-600">1,234+</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Lessons Completed</span>
                      <span className="font-semibold text-purple-600">45,678+</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Success Rate</span>
                      <span className="font-semibold text-green-600">94%</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  )
}

export default Home
